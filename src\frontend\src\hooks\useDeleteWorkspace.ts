import { useState } from "react";
import { workspaceChatDelete } from "../utils/deleteChatAgainstWorkspace";
import { RootState } from "../store";
import { Workspace } from "../interfaces";
import { useAppDispatch, useAppSelector } from "../app/hooks";
import { useDeleteWorkspaceMutation } from "../services/api/workspacesApi";

const useDeleteWorkspace = (setWorkspaces: React.Dispatch<React.SetStateAction<Workspace[]>>) => {
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deletingWorkspaceId, setDeletingWorkspaceId] = useState<string | null>(null);
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  // Use typed selector hook for better type safety
  const currentChatId = useAppSelector((state: RootState) => state.currentChatId.id);
  // Use typed selector hook for better type safety
  const allChatHistory = useAppSelector((state: RootState) => state.allChat.allChat);
  // Use RTK Query mutation hook
  const [deleteWorkspaceMutation] = useDeleteWorkspaceMutation();

  const handleDelete = async (id: string) => {
    try {
      setDeletingWorkspaceId(id);


      // First delete related chats from IndexedDB and Redux store
      await workspaceChatDelete({ allChat: allChatHistory }, id, currentChatId, dispatch);

      // Then delete the workspace from the backend using RTK Query
      await deleteWorkspaceMutation(id).unwrap();

      // Update the local state to remove the deleted workspace
      // This is for backward compatibility, as RTK Query should automatically update the cache
      setWorkspaces(prevWorkspaces => prevWorkspaces.filter(workspace => workspace.id !== id));
    } catch (error) {
      console.error("Error deleting workspace:", error);
      setDeleteError("Failed to delete workspace.");
      throw error; // Re-throw the error so the UI can handle it
    } finally {
      setDeletingWorkspaceId(null);
    }
  };

  return { handleDelete, deleteError, deletingWorkspaceId };
};

export default useDeleteWorkspace;