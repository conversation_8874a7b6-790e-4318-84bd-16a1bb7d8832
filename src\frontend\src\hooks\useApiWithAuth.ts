import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useAuth } from './useAuth';
import { setToken } from '../features/tokenSlice';

/**
 * Custom hook to ensure API calls have a valid authentication token
 * This hook should be used in components that make API calls
 * It will automatically refresh the token if needed
 */
const useApiWithAuth = () => {
  const { getAuthResult, activeAccount } = useAuth();
  const dispatch = useDispatch();

  useEffect(() => {
    const refreshToken = async () => {
      if (activeAccount) {
        try {
          const authResult = await getAuthResult();
          if (authResult) {
            dispatch(setToken(authResult.accessToken));
          }
        } catch (error) {
          console.error('Error refreshing token:', error);
        }
      }
    };

    refreshToken();
  }, [activeAccount, getAuthResult, dispatch]);

  return { isAuthenticated: !!activeAccount };
};

export default useApiWithAuth;
