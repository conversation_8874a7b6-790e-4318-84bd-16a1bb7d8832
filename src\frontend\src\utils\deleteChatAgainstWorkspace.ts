import { deleteData, getAllData } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { clearMessages } from "../features/chatSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { setCurrentWorkspaceId } from "../features/currentWorkspaceIdSlice";
import { clearRecentChat } from "../features/recentChatSlice";
import { deleteChatArray } from "../features/AllChatHistorySlice";
import { AppDispatch } from "../store";
import { AllChatState } from "../interfaces";

export const workspaceChatDelete = async (allChatHistory:AllChatState, workspaceId: string, currentChatId: string, dispatch: AppDispatch): Promise<void> => {
    const allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);
    if(allDbData.length > 0){
        const allDbId : string[] = [];
        for(const obj of allDbData){
            if(obj.workspaceId === workspaceId){
                allDbId.push(obj.id)
            }
        }

        const sameChatWindowFlag = allDbId.includes(currentChatId);

        // Use Promise.all to wait for all deletions to complete
        await Promise.all(allDbId.map(id => deleteData(Stores.Users, id)));

        if(sameChatWindowFlag){
            dispatch(clearMessages());
            dispatch(setCurrentChatId(""));
            dispatch(setCurrentWorkspaceId(""));
        }
        dispatch(clearRecentChat());

        if(!allChatHistory.allChat){
            return;
        }

        const ids = Object.keys(allChatHistory.allChat);
        let chatData = ids.map((id) => allChatHistory.allChat[id]);

        // Process all chat data and dispatch actions
        chatData.forEach((chat, ind) => {
            // Check if chat array exists and has at least one message before accessing properties
            if (chat && chat.length > 0 && chat[0].workspaceId === workspaceId) {
                dispatch(deleteChatArray({key: ids[ind]}))
            }
        });
    }
}