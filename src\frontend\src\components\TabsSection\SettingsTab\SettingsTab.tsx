import React, { useCallback } from "react";
import { addCustomTemplateButton, deleteCustomTemplateButton, FREQUENCY_PENALTY, PRESENCE_PENALTY, TEMPERATURE, TEMPLATE_SETTINGS, TOP_P } from "../../../constants/SettingsTabConstants";
import useSettings from "../../../hooks/useSettings";
import SettingSlider from "./SettingSlider";
import { UserSettingsTypes } from "../../../interfaces";
import { CheckmarkCircle24Filled, Info16Regular } from "@fluentui/react-icons";
import Toast from "../../Modal/ToastModal/Toast";
import useToast from "../../../hooks/useToast";
import { appInsights } from "../../../services/appInsightService";
import Tooltip from "../../Tooltip/Tooltip";
import TextareaAutosize from 'react-textarea-autosize';
import { useNavigate } from "react-router";

const settingsConfig: { key: keyof UserSettingsTypes; config: typeof TEMPERATURE }[] = [
  { key: "temperature", config: TEMPERATURE },
  { key: "topP", config: TOP_P },
  { key: "frequencyPenalty", config: FREQUENCY_PENALTY },
  { key: "presencePenalty", config: PRESENCE_PENALTY },
];

const SettingsTab: React.FC = React.memo(() => {
  const { settings, setSettings, saveSettings, resetSettings, selectedTemplateLabel, isTextareaDisabled, isButtonDisabled, handleTemplateChange, handleTextareaChange, textareaValue, templateList, isLoading } = useSettings();
  const navigate = useNavigate();
  const { toasts, triggerToast, closeToast } = useToast();

  // Handle slider change
  const handleChange = (key: keyof UserSettingsTypes, value: number) => {
    setSettings({ ...settings, [key]: value });
  };

  // Handle save button click
  const handleSaveSettings = () => {
    saveSettings(settings);
    triggerToast({
      text: "The settings were saved",
      icon: <CheckmarkCircle24Filled />,
      duration: 2,
      position: "bottom-center",
    });
    appInsights.trackEvent({ name: "SettingsModified" });
  };

  // Handle reset button click
  const handleResetSettings = () => {
    resetSettings();
    triggerToast({
      text: "Settings reset to default",
      icon: <CheckmarkCircle24Filled />,
      duration: 2,
      position: "bottom-center",
    });
    appInsights.trackEvent({ name: "SettingsDefault" });
  };

  const sortedTemplateList = [
    ...templateList.filter((val) => val.default),
    ...templateList.filter((val) => !val.default).sort((a, b) => a.label.localeCompare(b.label)),
  ];

  // Render the component
  return (
    <div className="h-full flex justify-center bg-white dark:bg-zinc-800 dark:text-white">
      <div className="flex flex-col h-full p-4 mb-6 space-y-4 overflow-y-auto w-full max-w-(--breakpoint-lg) md:w-1/2 xl:w-1/3 pb-[56px]">
        <h2 className="text-xl font-semibold text-left">Settings</h2>
        <h1 className="font-semibold mt-6 mb-6 text-xl">
          Instruction Settings
        </h1>
        <div className="flex items-center ">
          <h3 className="font-semibold">{TEMPLATE_SETTINGS.label}</h3>
          <Tooltip
            message={TEMPLATE_SETTINGS.description}
            position="bottom"
            className="w-[200px] absolute"
          >
            <Info16Regular className="ml-2 text-black dark:text-white" />
          </Tooltip>
        </div>
        <div className="flex flex-col mt-2 flex-grow">
          <button
            className={`flex items-center justify-center w-full p-2 mb-2 border-2 rounded bg-white dark:bg-zinc-700 dark:text-white dark:border-gray-600 text-black ${
              isButtonDisabled
                ? "cursor-not-allowed opacity-50"
                : "hover:bg-gallagher-dark-300 hover:text-white hover:border-gallagher-dark-300 cursor-pointer"
            }`}
            disabled={isButtonDisabled}
            aria-label={addCustomTemplateButton.label}
            onClick={() => {
              navigate("/add-assistant-instructions-template");
            }}
          >
            {addCustomTemplateButton.label}
          </button>
          {templateList.length > 1 && (
            <button
              className={`flex items-center justify-center w-full p-2 mb-2 border-2 rounded bg-white dark:bg-zinc-700 dark:text-white dark:border-gray-600 text-black hover:bg-gallagher-dark-300 hover:text-white hover:border-gallagher-dark-300 cursor-pointer`}
              aria-label={deleteCustomTemplateButton.label}
              onClick={() =>
                navigate("/delete-assistant-instructions-template")
              }
            >
              {deleteCustomTemplateButton.label}
            </button>
          )}
          <select
            className="w-full p-2 border-2 rounded dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300"
            onChange={handleTemplateChange}
            value={selectedTemplateLabel}
          >
            {sortedTemplateList.map((val) => (
              <option
                value={val.label}
                key={val.id}
                id={val.id}
                className=" whitespace-nowrap text-ellipsis"
                title={val.label}
              >
                {val.label}
              </option>
            ))}
          </select>

          <TextareaAutosize
            minRows={3}
            maxLength={3000}
            maxRows={10}
            className={`w-full h-24 mt-2 p-2 border-2 rounded dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 ${
              isTextareaDisabled ? "cursor-not-allowed opacity-50" : ""
            }`}
            aria-label="Customize your template here"
            placeholder="Customize your template here"
            value={textareaValue}
            onChange={handleTextareaChange}
            readOnly={isTextareaDisabled}
          />
          <div
            className={`text-sm text-right mt-1 ${
              textareaValue.length > 2950
                ? "text-red-500"
                : "text-gray-500 dark:text-gray-400"
            }`}
          >
            {textareaValue.length} / 3000 characters
          </div>
        </div>
        <h1 className="font-semibold mt-6 text-xl">Creativity Settings</h1>
        {settingsConfig.map(({ key, config }) => (
          <SettingSlider
            key={key}
            setting={config}
            value={settings[key] as number}
            onChange={(value) => handleChange(key, value)}
          />
        ))}

        <div className="flex space-x-2 fixed bottom-0 pl-0 pr-7 py-4 bg-white dark:bg-zinc-800 w-full max-w-(--breakpoint-lg) md:w-1/2 xl:w-1/3">
          <button
            onClick={handleResetSettings}
            className="w-1/2 cursor-pointer px-4 py-2 mt-6 border-2 rounded-sm dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 hover:text-white dark:hover:text-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300"
            disabled={isLoading}
          >
            Default
          </button>
          <button
            onClick={handleSaveSettings}
            className="w-1/2 cursor-pointer px-4 py-2 mt-6 border-2 rounded-sm dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 hover:text-white dark:hover:text-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300"
            disabled={isLoading}
          >
            Save
          </button>
        </div>
      </div>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          id={toast.id}
          position={toast.position}
          text={toast.text}
          icon={toast.icon}
          duration={toast.duration}
          onClose={() => closeToast(toast.id)}
          bgColor={toast.bgColor}
        />
      ))}
    </div>
  );
});

export default SettingsTab;
