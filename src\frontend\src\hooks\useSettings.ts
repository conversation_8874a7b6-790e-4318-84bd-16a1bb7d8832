<<<<<<< HEAD
import { useState, useEffect } from 'react';
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY, SYSTEM_TEMPLATE, CUSTOM_TEMPLATE_LIMIT } from "../constants/SettingsTabConstants";
import { Stores, USER_SETTINGS_ID } from "../constants/dbConstants";
import { initUserSettingDB, getUserSetting, saveUserSetting, getCustomTemplate, updateOrSaveCustomTemplate, getSelectedCustomTemplate, saveSelectedCustomTemplate } from "../db/settingDB";
=======
import { useState, useEffect, useCallback } from 'react';
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY } from "../constants/SettingsTabConstants";
import { USER_SETTINGS_ID } from "../constants/dbConstants";
>>>>>>> 329a7f5 (fix: settings not changing)
import { UserSettingsTypes } from '../interfaces';
import { useAppDispatch, useAppSelector } from "../app/hooks";
import { setSetting } from '../features/settingsSlice';
<<<<<<< HEAD
import { CustomTemplatePropType, MergedTemplateListPropTypes } from '../types/dbTypes';
import { removeDuplicates } from '../utils/removeDuplicateTemplates';
import { setSessionCallFlag } from '../features/sessionFlagSlice';
import { RootState } from '../store';
=======
import { useAppDispatch, useAppSelector } from '../app/hooks';
>>>>>>> 329a7f5 (fix: settings not changing)
import {
  useGetUserSettingsQuery,
  useSaveUserSettingsMutation,
  useResetUserSettingsMutation
} from '../services/api/settingsApi';

const useSettings = () => {
  // Default settings for initial state
  const defaultSettings: UserSettingsTypes = {
    id: USER_SETTINGS_ID,
    temperature: TEMPERATURE.defaultValue,
    topP: TOP_P.defaultValue,
    frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
    presencePenalty: PRESENCE_PENALTY.defaultValue,
    template: SYSTEM_TEMPLATE.find((template) => template.default === true)?.description || ""
  };

<<<<<<< HEAD
  // Local state for template management
  const [selectedTemplateLabel, setSelectedTemplateLabel] = useState<string>("");
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");
  const [isTextareaDisabled, setIsTextareaDisabled] = useState<boolean>(true);
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(false);
  const [textareaValue, setTextareaValue] = useState<string>(SYSTEM_TEMPLATE[0].description);
  const [templateList, setTemplateList] = useState<MergedTemplateListPropTypes[]>(SYSTEM_TEMPLATE);
  const [customTemplateDB, setCustomTemplateDB] = useState<CustomTemplatePropType[]>([]);
  const [localSettings, setLocalSettings] = useState<UserSettingsTypes>(defaultSettings);
=======
  // Get settings from Redux store
  const reduxSettings = useAppSelector(state => state.settings);
>>>>>>> 329a7f5 (fix: settings not changing)

  // Redux hooks
  const newSessionFlag = useAppSelector((state: RootState) => state.sessionFlag.status);
  const dispatch = useAppDispatch();

  // RTK Query hooks
  const { data: fetchedSettings, isLoading } = useGetUserSettingsQuery(undefined, {
    // Skip initial fetch if we already have settings in Redux
    skip: false
  });
  const [saveUserSettings, { isLoading: isSaving }] = useSaveUserSettingsMutation();
  const [resetUserSettings, { isLoading: isResetting }] = useResetUserSettingsMutation();


<<<<<<< HEAD
  // Combine fetched settings with local state
  const settings = fetchedSettings || localSettings;
  // Update Redux store when settings change
=======
  // Local state for settings that are being edited - initialize with default settings
  // This ensures we always have valid settings even before data is loaded
  const [editedSettings, setEditedSettings] = useState<UserSettingsTypes>(defaultSettings);

  // Update local state when Redux or fetched settings change
  useEffect(() => {
    // Use fetched settings if available, otherwise use Redux settings, or fall back to defaults
    const currentSettings = fetchedSettings || reduxSettings || defaultSettings;
    setEditedSettings(currentSettings);
  }, [fetchedSettings, reduxSettings]);

  // Update Redux store when settings are fetched
>>>>>>> 329a7f5 (fix: settings not changing)
  useEffect(() => {
    if (fetchedSettings && !isLoading) {
      dispatch(setSetting(fetchedSettings));
    }
  }, [fetchedSettings, isLoading, dispatch]);
<<<<<<< HEAD

  const fetchTemplates = async () => {
    await initUserSettingDB();
    const customTemplate = await getCustomTemplate();
    setCustomTemplateDB(customTemplate);
    const uniqueTemplates = removeDuplicates([...SYSTEM_TEMPLATE, ...customTemplate]);
    setTemplateList(uniqueTemplates);
  };

  useEffect(() => {
    if (newSessionFlag) {
      async function init() {
        await initUserSettingDB();
        await saveSelectedCustomTemplate(SYSTEM_TEMPLATE[0]);
        const defaultSettings = {
          id: USER_SETTINGS_ID,
          temperature: TEMPERATURE.defaultValue,
          topP: TOP_P.defaultValue,
          frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
          presencePenalty: PRESENCE_PENALTY.defaultValue,
          template: "",
        };
        const settings = await getUserSetting(USER_SETTINGS_ID);
        if (!settings) {
          saveUserSetting(defaultSettings);
          dispatch(setSetting(defaultSettings));
        } else {
          settings.template = "";
          saveUserSetting(settings);
        }
        dispatch(setSessionCallFlag(false));
        dispatch(setSetting(settings));
      }
      init();
    }
  }, [newSessionFlag, dispatch]);

  useEffect(() => {
    fetchTemplates();
  }, []);

  useEffect(() => {
    const customTemplates = templateList.filter((template) => template.template === "custom");
    setIsButtonDisabled(customTemplates.length >= CUSTOM_TEMPLATE_LIMIT);
  }, [templateList]);

  useEffect(() => {
    const fetchData = async () => {
      await initUserSettingDB();
      const savedSettings = await getUserSetting(Stores.UserSettings);
      const customTemplate = await getCustomTemplate();
      const selectedTemplate = await getSelectedCustomTemplate();

      if (savedSettings) {
        setSettings(savedSettings);
        dispatch(setSetting(savedSettings));
        if (selectedTemplate) {
          setSelectedTemplateLabel(selectedTemplate.label);
          setSelectedTemplateId(selectedTemplate.id);
          setTextareaValue(selectedTemplate.description);
          setIsTextareaDisabled(selectedTemplate.template === "system");
        } else {
          const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.description === savedSettings.template);
          if (defaultTemplate) {
            setSelectedTemplateLabel(defaultTemplate.label);
            setSelectedTemplateId(defaultTemplate.id);
            setIsTextareaDisabled(defaultTemplate.template === "system");
            setTextareaValue(defaultTemplate.description);
          }
        }
      } else {
        const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
        if (defaultTemplate) {
          setSelectedTemplateLabel(defaultTemplate.label);
          setSelectedTemplateId(defaultTemplate.id);
          setIsTextareaDisabled(defaultTemplate.template === "system");
          setTextareaValue(defaultTemplate.description);
        }
      }
      const uniqueTemplates = removeDuplicates([...SYSTEM_TEMPLATE, ...customTemplate]);
      setTemplateList(uniqueTemplates);
    };
    fetchData();
  }, [dispatch]);

  useEffect(() => {
    setTemplateList(prevTemplateList => {
      const updatedList = removeDuplicates([...prevTemplateList, ...customTemplateDB]);
      return updatedList;
    });
    const isNewSelectedTemplate = templateList.some((template) => template.id === selectedTemplateId);
    if (!isNewSelectedTemplate) {
      const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
      if (defaultTemplate) {
        setSelectedTemplateLabel(defaultTemplate.label);
        setSelectedTemplateId(defaultTemplate.id);
        setTextareaValue(defaultTemplate.description);
        setIsTextareaDisabled(defaultTemplate.template === "system");
      }
    }
  }, [customTemplateDB]);

  const handleTemplateChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedLabel = event.target.value;
    const selectedTemplate = templateList.find((template) => template.label === selectedLabel);
    if (selectedTemplate) {
      setTextareaValue(selectedTemplate.description);
      setSelectedTemplateLabel(selectedLabel);
      setSelectedTemplateId(selectedTemplate.id);
      setIsTextareaDisabled(selectedTemplate.template === "system");
    }
  };

  const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextareaValue(event.target.value);
  };

  // Save settings function - combines RTK Query with template management
  const saveSettings = async (newSettings: UserSettingsTypes) => {
=======

  // Function to update a single setting
  const updateSetting = useCallback((key: keyof UserSettingsTypes, value: number) => {
    setEditedSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // Save settings function
  const saveSettings = async () => {
>>>>>>> 329a7f5 (fix: settings not changing)
    try {
      const updatedSettings = { ...newSettings, template: textareaValue === SYSTEM_TEMPLATE[0].description ? "" : textareaValue };

      // Save to IndexedDB via RTK Query
<<<<<<< HEAD
      await saveUserSettings(updatedSettings).unwrap();

      // Update template management
      await updateOrSaveCustomTemplate(selectedTemplateId, textareaValue, templateList);

      // Update local state
      setLocalSettings(updatedSettings);

      // Update Redux store
      dispatch(setSetting(updatedSettings));
=======
      const result = await saveUserSettings(editedSettings).unwrap();

      // Update Redux store
      dispatch(setSetting(result));
>>>>>>> 329a7f5 (fix: settings not changing)
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  // Reset settings function - combines RTK Query with template management
  const resetSettings = async () => {
    try {
      const defaultSettings = {
        id: USER_SETTINGS_ID,
        temperature: TEMPERATURE.defaultValue,
        topP: TOP_P.defaultValue,
        frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
        presencePenalty: PRESENCE_PENALTY.defaultValue,
        template: "",
      };

      // Reset via RTK Query
      const result = await resetUserSettings().unwrap();

      // Reset template management
      await saveSelectedCustomTemplate(SYSTEM_TEMPLATE.filter((template) => template.default === true)[0]);

      // Update local state
      setEditedSettings(result);

      // Update Redux store
      dispatch(setSetting(result));

      // Reset template UI state
      const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
      if (defaultTemplate) {
        setSelectedTemplateLabel(defaultTemplate.label);
        setSelectedTemplateId(defaultTemplate.id);
        setIsTextareaDisabled(defaultTemplate.template === "system");
        setTextareaValue(defaultTemplate.description);
      }

      console.log('Reset Settings:', result); // Log reset settings
    } catch (error) {
      console.error('Failed to reset settings:', error);
    }
  };


  return {
<<<<<<< HEAD
    settings,
    selectedTemplateLabel,
    isTextareaDisabled,
    isButtonDisabled,
    textareaValue,
    setSettings: setLocalSettings, // Expose setSettings to update local state without saving
    handleTemplateChange,
    handleTextareaChange,
    saveSettings,
    resetSettings,
    templateList,
    isLoading,
=======
    settings: editedSettings,
    updateSetting,
    saveSettings,
    resetSettings,
    isLoading: isLoading || isSaving || isResetting
>>>>>>> 329a7f5 (fix: settings not changing)
  };
};

export default useSettings;