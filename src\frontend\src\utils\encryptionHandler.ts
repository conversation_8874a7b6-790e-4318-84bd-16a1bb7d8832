import { addData, getAllData, deleteData, updateHash } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { setApiCallFlag } from "../features/apiCallFlagSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { addChatArray, updateChatArray } from "../features/AllChatHistorySlice";
import { AppDispatch } from "../store";
import { chatEncryption } from "./chatEncryption";
import { chatMessagesTransform } from "./chatMessagesTransform";
import { ChatMessageProps } from "./types";
import { MAX_CHAT_WINDOW_LENGTH } from "../constants/constants";

export const encryptionHandler = async (chatMessages: ChatMessageProps[], email: string, token: string, dispatch: AppDispatch, currentChatId: string | undefined, currentWorkspaceId: string) => {
    try {
        // Transform messages for encryption
        const transformedMessages = await chatMessagesTransform(chatMessages);
        let workspaceId = currentWorkspaceId;

        // Encrypt the messages
        const encryptedMessage = await chatEncryption(transformedMessages, email, token);

        // Get all current data from the database
        let allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

        // Case 1: New chat (no current chat ID)
        if(!currentChatId && encryptedMessage){
            // Generate encrypted data with a unique ID
            const encryptedData = await transformEncryptedData(encryptedMessage, workspaceId);
            const newData = [{ ...encryptedData }];

            // Add to the database (this will also remove the oldest chat if necessary)
            await addData(Stores.Users, newData);

            // Get updated data from the database
            const newDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

            // Sort by date (most recent first)
            const sortedData = [...newDbData].sort((a, b) =>
                new Date(b.date).getTime() - new Date(a.date).getTime()
            );

            // Find the new chat (should be the most recent)
            const newChat = sortedData.find(data => data.hash === encryptedMessage);

            if (newChat) {
                // Add the new chat to the global state
                dispatch(addChatArray({key: newChat.id, chatArray: chatMessages}));

                // Set the current chat ID
                dispatch(setCurrentChatId(newChat.id));
            }
        }

        // Case 2: Update an existing chat
        else if (allDbData.length > 0 && currentChatId) {
            const chatExists = allDbData.some(data => data.id === currentChatId);

            // Get the workspace ID of the current chat
            for(const elem of allDbData){
                if(elem.id === currentChatId){
                    workspaceId = elem.workspaceId;
                }
            }

            if (chatExists) {
                // Update the hash of the existing chat
                await updateHash(Stores.Users, currentChatId, encryptedMessage ? encryptedMessage : "");

                // Update the chat in the global state
                dispatch(updateChatArray({key: currentChatId, chatArray: chatMessages}));
            }
        }

        // Finish the operation
        dispatch(setApiCallFlag(false));
    } catch (error) {
        console.error('Error in encryptionHandler:', error);
        dispatch(setApiCallFlag(false));
    }
};

async function transformEncryptedData(encryptedMessage: string, workspaceId:string) {
    const date = new Date();
    const isoString = date.toISOString();

    const allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

    // Sort data by date (oldest first)
    const sortedData = [...allDbData].sort((a, b) =>
        new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Extract numeric IDs (1-10)
    const numericIds = sortedData
        .map(data => data.id)
        .filter(id => /^\d+$/.test(id))
        .map(id => parseInt(id, 10));

    // Find the first available ID between 1 and 10
    let nextId = 1;
    while (numericIds.includes(nextId) && nextId <= MAX_CHAT_WINDOW_LENGTH) {
        nextId++;
    }

    // If all IDs from 1 to 10 are in use, use the ID of the oldest chat
    // which will be replaced by this new chat
    if (nextId > MAX_CHAT_WINDOW_LENGTH && sortedData.length >= MAX_CHAT_WINDOW_LENGTH) {
        // Use the ID of the oldest chat
        nextId = parseInt(sortedData[0].id, 10);
        // If it's not a number, use 1 as fallback
        if (isNaN(nextId)) {
            nextId = 1;
        }
    }

    return {
        hash: encryptedMessage,
        date: isoString,
        id: nextId.toString(),
        workspaceId: workspaceId
    };
}

export function makeAlphanumeric(dateString: string) {
    return dateString.replace(/[^a-zA-Z0-9]/g, '');
}
